{% load inventory_filters %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Inventory Management System{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Organization-specific theme -->
    {% if request.session.org_id %}
        {% with org=organizations|filter_by_id:request.session.org_id %}
            <style>
                :root {
                    --primary: {{ org.primary_color }};
                    --secondary: {{ org.secondary_color }};
                    --bs-primary: {{ org.primary_color }};
                    --bs-primary-rgb: {{ org.primary_color|hex_to_rgb }};
                    --bs-secondary: {{ org.secondary_color }};
                    --bs-secondary-rgb: {{ org.secondary_color|hex_to_rgb }};
                }

                .bg-primary {
                    background-color: var(--primary) !important;
                }

                .bg-secondary {
                    background-color: var(--secondary) !important;
                }

                .btn-primary {
                    background-color: var(--primary);
                    border-color: var(--primary);
                }

                .btn-outline-primary {
                    color: var(--primary);
                    border-color: var(--primary);
                }

                .btn-outline-primary:hover {
                    background-color: var(--primary);
                    border-color: var(--primary);
                }

                .navbar-dark {
                    background-color: var(--primary) !important;
                }

                a {
                    color: var(--primary);
                }

                .page-item.active .page-link {
                    background-color: var(--primary);
                    border-color: var(--primary);
                }
            </style>
        {% endwith %}
    {% endif %}

    <!-- Custom CSS -->
    <style>
        .navbar { margin-bottom: 20px; }
        .item-container { border-left: 3px solid var(--primary, #007bff); padding-left: 10px; }
        .item-image { max-width: 150px; max-height: 150px; }
        .code-box {
            padding: 10px;
            border: 1px solid #ddd;
            display: inline-block;
            font-family: monospace;
            font-size: 1.2em;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }
    </style>
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.9"></script>

    <!-- Extra CSS block for page-specific styles -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'inventory:item_list' %}">
                {% if request.session.org_id %}
                    {% with org=organizations|filter_by_id:request.session.org_id %}
                        {% if org.logo %}
                            <img src="{{ org.logo.url }}" alt="{{ org.name }}" height="30" class="me-2">
                        {% endif %}
                        {{ org.name }} Inventory
                    {% endwith %}
                {% else %}
                    Inventory System
                {% endif %}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:item_list' %}">Items</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:add_item' %}">Add Item</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:item_lookup' %}">Item Lookup</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'inventory:generate_labels' %}">Generate Labels</a>
                    </li>
                    {% if user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">Admin</a>
                    </li>
                    {% endif %}
                    <!-- Remove the Manage Users link -->
                </ul>

                <!-- Organization Selector Dropdown -->
                {% if user_org_count > 1 %}
                <div class="dropdown me-3">
                    <button class="btn btn-outline-light dropdown-toggle" type="button" id="orgDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        {% if request.session.org_id %}
                            {% with org=organizations|filter_by_id:request.session.org_id %}
                                {{ org.name }}
                            {% endwith %}
                        {% else %}
                            Select Organization
                        {% endif %}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="orgDropdown">
                        {% for org in organizations %}
                            <li>
                                <a class="dropdown-item {% if org.id|stringformat:'i' == request.session.org_id %}active{% endif %}"
                                   href="{% url 'inventory:switch_organization' org_id=org.id %}?next={{ request.path }}">
                                    {% if org.logo %}
                                        <img src="{{ org.logo.url }}" alt="{{ org.name }}" height="20" class="me-2">
                                    {% endif %}
                                    {{ org.name }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                {% elif user_org_count == 1 and request.session.org_id %}
                <div class="me-3">
                    {% with org=organizations|filter_by_id:request.session.org_id %}
                        <span class="text-light">{{ org.name }}</span>
                    {% endwith %}
                </div>
                {% endif %}

                <div class="navbar-nav">
                    {% if user.is_authenticated %}
                        <span class="nav-link">{{ user.username }}</span>
                        <a class="nav-link" href="{% url 'logout' %}">Logout</a>
                    {% else %}
                        <a class="nav-link" href="{% url 'login' %}">Login</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>

