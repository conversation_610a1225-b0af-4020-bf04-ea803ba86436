{% extends 'base.html' %}
{% load static %}

{% block title %}{% if is_add %}Add New Item{% else %}Edit {{ item.item_name }}{% endif %} - Inventory Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<!-- Include inline CSS first to ensure styles are applied immediately -->
{% include 'inventory/inline_css.html' %}

<!-- Primary CSS link with cache-busting parameter -->
<link rel="stylesheet" type="text/css" href="{% static 'css/item_code_lookup.css' %}?v={% now 'U' %}" id="item-code-lookup-css">

<!-- Debug information for CSS loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Checking for CSS link element...');
    const cssLink = document.getElementById('item-code-lookup-css');
    if (cssLink) {
        console.log('CSS link found:', cssLink.outerHTML);
    } else {
        console.error('CSS link not found in the DOM');
        console.log('All link elements:', document.querySelectorAll('link'));
    }
});
</script>
<style>
    /* Custom styles for the image formset */

    /* Image formset styling */
    .image-form-row {
        transition: background-color 0.2s;
    }
    .image-form-row:hover {
        background-color: #f8f9fa;
    }
    .image-form-row.ui-sortable-helper {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        background-color: #fff;
    }
    .image-form-row.ui-sortable-placeholder {
        visibility: visible !important;
        background-color: #f0f0f0;
        border: 2px dashed #ccc;
        height: 120px;
    }
    .drag-handle {
        cursor: grab;
    }
    .drag-handle:active {
        cursor: grabbing;
    }

    /* QR Scanner Styles */
    .location-field-container {
        position: relative;
    }

    .qr-scanner-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: 1060;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .qr-scanner-content {
        background: white;
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .qr-scanner-header {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .qr-scanner-header h5 {
        margin: 0;
        color: #212529;
    }

    .btn-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #6c757d;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close:hover {
        color: #000;
    }

    .qr-scanner-body {
        padding: 1rem;
        text-align: center;
    }

    .qr-video {
        width: 100%;
        max-width: 400px;
        height: auto;
        border-radius: 4px;
        background-color: #000;
    }

    .qr-scanner-status {
        margin-top: 1rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 4px;
        color: #6c757d;
    }

    .qr-scanner-status.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .qr-scanner-status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .qr-scanner-footer {
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        text-align: right;
    }

    /* iOS specific adjustments */
    @media screen and (max-width: 768px) {
        .qr-scanner-content {
            width: 95%;
            margin: 1rem;
        }

        .qr-video {
            max-width: 100%;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<!-- QR Scanner Library for iOS Safari compatibility -->
<script type="module" src="https://cdn.jsdelivr.net/npm/qr-scanner@1.4.2/qr-scanner.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Verify jQuery and jQuery UI are loaded
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded');
            return;
        }

        if (typeof jQuery.ui === 'undefined') {
            console.error('jQuery UI is not loaded');
            return;
        }

        // Initialize the item code lookup widget
        const lookupInputs = document.querySelectorAll('.item-code-lookup');

        lookupInputs.forEach(function(input) {
            const inputId = input.id;
            const hiddenInput = document.getElementById(`${inputId}_hidden`);
            const displayField = document.getElementById(`${inputId}_display`);
            const clearButton = document.getElementById(`${inputId}_clear`);

            // Initialize the widget
            if (hiddenInput && displayField && clearButton) {
                initializeWidget(input, hiddenInput, displayField, clearButton);

                // If there's an initial value, fetch the item details
                if (hiddenInput.value) {
                    fetchItemDetails(hiddenInput.value, displayField, clearButton, input);
                }
            }
        });

        /**
         * Initialize the widget with autocomplete and event handlers
         */
        function initializeWidget(input, hiddenInput, displayField, clearButton) {
            // Set up autocomplete
            $(input).autocomplete({
                source: function(request, response) {
                    // Get the current item ID if we're editing an existing item
                    const currentItemId = document.querySelector('input[name="current_item_id"]')?.value || '';

                    // Make AJAX request to the autocomplete endpoint
                    $.ajax({
                        url: '/inventory/api/items/autocomplete/',
                        dataType: 'json',
                        data: {
                            term: request.term,
                            current_item_id: currentItemId
                        },
                        success: function(data) {
                            // Transform the data for autocomplete
                            const items = data.results.map(function(item) {
                                return {
                                    label: item.code ? `${item.code} - ${item.text}` : item.text,
                                    value: item.code ? `${item.code} - ${item.text}` : item.text,
                                    id: item.id,
                                    code: item.code,
                                    text: item.text
                                };
                            });
                            response(items);
                        }
                    });
                },
                minLength: 2,
                select: function(event, ui) {
                    // Set the hidden input value to the selected item ID
                    hiddenInput.value = ui.item.id;

                    // Show the display field with the selected item
                    if (ui.item.code) {
                        displayField.value = `${ui.item.code} - ${ui.item.text}`;
                    } else {
                        displayField.value = ui.item.text;
                    }

                    // Show the display field and clear button
                    displayField.style.display = 'block';
                    clearButton.style.display = 'block';

                    // Hide the search input
                    input.style.display = 'none';

                    // Prevent the default behavior
                    return false;
                }
            }).autocomplete('instance')._renderItem = function(ul, item) {
                // Enhanced custom rendering for autocomplete items
                let html = '<div class="autocomplete-item">';

                if (item.code) {
                    html += `<div><code>${item.code}</code> - <strong>${item.text}</strong></div>`;
                    if (item.type) {
                        html += `<div class="item-type text-muted small">${item.type}</div>`;
                    }
                } else {
                    html += `<div><strong>${item.text}</strong></div>`;
                    if (item.type) {
                        html += `<div class="item-type text-muted small">${item.type}</div>`;
                    }
                }

                html += '</div>';

                return $('<li>')
                    .append(html)
                    .appendTo(ul);
            };

            // Handle clear button click
            clearButton.addEventListener('click', function() {
                // Clear the hidden input
                hiddenInput.value = '';

                // Clear and hide the display field
                displayField.value = '';
                displayField.style.display = 'none';

                // Hide the clear button
                clearButton.style.display = 'none';

                // Show and clear the search input
                input.style.display = 'block';
                input.value = '';
                input.focus();
            });
        }

        /**
         * Fetch item details for an initial value
         */
        function fetchItemDetails(itemId, displayField, clearButton, input) {
            if (!itemId) return;

            // Make AJAX request to get item details
            $.ajax({
                url: '/inventory/api/items/autocomplete/',
                dataType: 'json',
                data: {
                    term: itemId,  // Use the ID as the search term
                    exact_match: true
                },
                success: function(data) {
                    if (data.results && data.results.length > 0) {
                        const item = data.results[0];

                        // Set the display field
                        if (item.code) {
                            displayField.value = `${item.code} - ${item.text}`;
                        } else {
                            displayField.value = item.text;
                        }

                        // Show the display field and clear button
                        displayField.style.display = 'block';
                        clearButton.style.display = 'block';

                        // Hide the search input
                        if (input) {
                            input.style.display = 'none';
                        }
                    }
                }
            });
        }

        // Remove any reference to container_autocomplete

        // Handle adding custom fields
        const addButton = document.getElementById('add-custom-field');
        const container = document.getElementById('custom-fields-container');

        addButton.addEventListener('click', function() {
            const row = document.createElement('div');
            row.className = 'row mb-2 custom-field-row';
            row.innerHTML = `
                <div class="col-5">
                    <input type="text" class="form-control form-control-sm"
                           name="custom_field_key_new" placeholder="Field Name" required>
                </div>
                <div class="col-6">
                    <input type="text" class="form-control form-control-sm"
                           name="custom_field_value_new" placeholder="Value">
                </div>
                <div class="col-1">
                    <button type="button" class="btn btn-sm btn-outline-danger remove-field">&times;</button>
                </div>
            `;
            container.appendChild(row);

            // Focus on the new field
            row.querySelector('input[name="custom_field_key_new"]').focus();

            // Add event listener to the remove button
            row.querySelector('.remove-field').addEventListener('click', function() {
                row.remove();
            });
        });

        // Add event listeners to existing remove buttons
        document.querySelectorAll('.remove-field').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.custom-field-row').remove();
            });
        });

        // Handle form submission to process custom fields
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            // Find all new custom field rows
            const newFieldRows = document.querySelectorAll('input[name="custom_field_key_new"]');

            newFieldRows.forEach((keyInput, index) => {
                const valueInput = keyInput.closest('.row').querySelector('input[name="custom_field_value_new"]');

                if (keyInput.value.trim()) {
                    // Rename the inputs to have unique names with the actual key
                    keyInput.name = 'custom_field_' + keyInput.value.trim();
                    valueInput.name = 'custom_field_' + keyInput.value.trim();
                }
            });

            // Update order values for images based on their position
            updateImageOrder();
        });

        // Image formset handling (optional - only if formset exists)
        const imageFormsetContainer = document.getElementById('image-formset-container');
        const addImageBtn = document.getElementById('add-image');
        const totalFormsInput = document.querySelector('[name="item_images-TOTAL_FORMS"]');

        // Only initialize image formset if container exists
        if (imageFormsetContainer) {

            // Add image preview functionality
            imageFormsetContainer.addEventListener('change', function(e) {
            if (e.target.type === 'file') {
                const fileInput = e.target;
                const formRow = fileInput.closest('.image-form-row');
                const previewContainer = formRow.querySelector('.col-5');

                // Remove existing preview if any
                const existingPreview = previewContainer.querySelector('.mb-2');
                if (existingPreview) {
                    existingPreview.remove();
                }

                // Create new preview if file is selected
                if (fileInput.files && fileInput.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const previewDiv = document.createElement('div');
                        previewDiv.className = 'mb-2';
                        previewDiv.innerHTML = `
                            <img src="${e.target.result}" class="img-thumbnail" style="max-height: 100px;">
                        `;
                        previewContainer.insertBefore(previewDiv, previewContainer.firstChild);
                    }

                    reader.onerror = function() {
                        console.error('Error reading file');
                    }

                    reader.readAsDataURL(fileInput.files[0]);
                }
            }
            });

            // Add error handling for sortable initialization
            try {
                if ($.fn.sortable) {
                    $(imageFormsetContainer).sortable({
                    handle: '.drag-handle',
                    placeholder: 'image-form-row ui-sortable-placeholder',
                    forcePlaceholderSize: true,
                    update: function(event, ui) {
                        updateImageOrder();
                    }
                });
            }
        } catch (error) {
            console.error('Error initializing sortable:', error);
        }

            // Function to update ORDER fields based on current position
            function updateImageOrder() {
                if (!imageFormsetContainer) return;

                const forms = imageFormsetContainer.querySelectorAll('.image-form-row');
            forms.forEach((form, index) => {
                // Try to find the ORDER field first
                let orderInput = form.querySelector('input[name$="-ORDER"]');

                // If ORDER field doesn't exist, look for the order field
                if (!orderInput) {
                    orderInput = form.querySelector('input[name$="-order"]');
                }

                // If neither exists, create a hidden input for order
                if (!orderInput) {
                    const formPrefix = form.querySelector('input').name.split('-')[0];
                    const formIndex = form.getAttribute('data-id');

                    orderInput = document.createElement('input');
                    orderInput.type = 'hidden';
                    orderInput.name = `${formPrefix}-${formIndex}-order`;
                    orderInput.value = index;
                    form.appendChild(orderInput);
                } else {
                    orderInput.value = index;
                }
            });
        }

            // Function to update form indices
            function updateFormIndices() {
                if (!imageFormsetContainer) return;

                const forms = imageFormsetContainer.querySelectorAll('.image-form-row');
                forms.forEach((form, index) => {
                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach(input => {
                        if (input.name) {
                            input.name = input.name.replace(/item_images-\d+/, `item_images-${index}`);
                            input.id = input.id.replace(/id_item_images-\d+/, `id_item_images-${index}`);
                        }
                    });

                    const labels = form.querySelectorAll('label');
                    labels.forEach(label => {
                        if (label.htmlFor) {
                            label.htmlFor = label.htmlFor.replace(/id_item_images-\d+/, `id_item_images-${index}`);
                        }
                    });

                    // Update data-id attribute
                    form.setAttribute('data-id', index);
                });

                // Update total forms count
                if (totalFormsInput) {
                    totalFormsInput.value = forms.length;
                }
            }

            // Add new image form - with additional checks
            if (addImageBtn) {
                addImageBtn.addEventListener('click', function() {
            const forms = imageFormsetContainer.querySelectorAll('.image-form-row');

            if (forms.length === 0) {
                // No existing forms, create a new template
                const newForm = document.createElement('div');
                newForm.className = 'image-form-row mb-3 p-2 border rounded bg-light';
                newForm.setAttribute('data-id', '0');

                // Create the form structure
                newForm.innerHTML = `
                    <div class="d-flex align-items-center mb-2">
                        <div class="drag-handle me-2">
                            <i class="fas fa-grip-lines"></i>
                        </div>
                        <div class="flex-grow-1">
                            <small class="text-muted">Drag to reorder</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-5">
                            <input type="file" name="item_images-0-image" class="form-control">
                            <input type="hidden" name="item_images-0-ORDER" value="0">
                        </div>
                        <div class="col-4">
                            <input type="text" name="item_images-0-caption" class="form-control" placeholder="Caption">
                        </div>
                        <div class="col-2">
                            <div class="form-check mt-2">
                                <input type="checkbox" name="item_images-0-is_primary" class="form-check-input" id="id_item_images-0-is_primary">
                                <label class="form-check-label" for="id_item_images-0-is_primary">Featured</label>
                            </div>
                        </div>
                        <div class="col-1">
                            <!-- No delete button for first form -->
                        </div>
                    </div>
                `;

                // Add the new form to the container
                imageFormsetContainer.appendChild(newForm);

                // Update total forms count
                totalFormsInput.value = 1;

                return;
            }

            // Clone existing form logic (unchanged)
            const formCount = forms.length;
            const firstForm = forms[0];
            const newForm = firstForm.cloneNode(true);

            // Clear values in the new form
            const inputs = newForm.querySelectorAll('input:not([type="hidden"]), select, textarea');
            inputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else {
                    input.value = '';
                }
            });

            // Remove any existing image preview
            const imgPreview = newForm.querySelector('img');
            if (imgPreview) {
                imgPreview.parentNode.remove();
            }

            // Update form index
            const newIndex = formCount;
            const allInputs = newForm.querySelectorAll('input, select, textarea');
            allInputs.forEach(input => {
                if (input.name) {
                    input.name = input.name.replace(/item_images-\d+/, `item_images-${newIndex}`);
                    input.id = input.id.replace(/id_item_images-\d+/, `id_item_images-${newIndex}`);
                }
            });

            // Update data-id attribute
            newForm.setAttribute('data-id', newIndex);

            // Add the new form to the container
            imageFormsetContainer.appendChild(newForm);

            // Update total forms count
            totalFormsInput.value = parseInt(totalFormsInput.value) + 1;

                // Attach event handlers to the new form
                attachImageFormEventHandlers(newForm);
                });
            }

            // Handle primary image selection
            imageFormsetContainer.addEventListener('change', function(e) {
            if (e.target.name && e.target.name.includes('-is_primary') && e.target.checked) {
                // Uncheck all other primary checkboxes
                const primaryCheckboxes = imageFormsetContainer.querySelectorAll('input[name$="-is_primary"]');
                primaryCheckboxes.forEach(checkbox => {
                    if (checkbox !== e.target) {
                        checkbox.checked = false;
                    }
                });
            }
        });

            // Handle delete buttons
            function attachImageFormEventHandlers(formRow) {
            const deleteBtn = formRow.querySelector('.delete-image-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', function() {
                    Swal.fire({
                        title: 'Delete Image?',
                        text: 'Are you sure you want to delete this image? This cannot be undone.',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#dc3545',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Yes, delete it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Check the hidden DELETE checkbox
                            const deleteCheckbox = formRow.querySelector('input[name$="-DELETE"]');
                            if (deleteCheckbox) {
                                deleteCheckbox.checked = true;
                                // Visually hide the row but keep it in the DOM for form submission
                                formRow.style.display = 'none';
                            } else {
                                // If it's a new form (not saved yet), just remove it
                                formRow.remove();
                                updateFormIndices();
                            }
                        }
                    });
                });
            }
        }

            // Attach event handlers to all existing image forms
            document.querySelectorAll('.image-form-row').forEach(formRow => {
                attachImageFormEventHandlers(formRow);
            });
        }

        // QR Scanner functionality
        initializeQRScanner();
    });

    // QR Scanner Implementation
    function initializeQRScanner() {
        // iOS/Safari detection
        const userAgent = navigator.userAgent;
        const isIOS = /iPad|iPhone|iPod/.test(userAgent);
        const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
        const isWebKit = /WebKit/.test(userAgent);

        // Show button on iOS devices or Safari browsers
        const shouldShowButton = isIOS || isSafari || isWebKit;

        // Get all DOM elements
        const qrButton = document.getElementById('qr-scanner-btn');
        const qrModal = document.getElementById('qr-scanner-modal');
        const qrVideo = document.getElementById('qr-video');
        const closeScanner = document.getElementById('close-scanner');
        const cancelScanner = document.getElementById('cancel-scanner');
        const statusDiv = document.getElementById('qr-scanner-status');

        if (qrButton && shouldShowButton) {
            qrButton.style.display = 'inline-block';
        }

        let qrScanner = null;
        let isScanning = false;

        // Open QR scanner
        if (qrButton) {
            qrButton.addEventListener('click', async function() {
                try {
                    await startQRScanner();
                } catch (error) {
                    console.error('Failed to start QR scanner:', error);
                    showScannerStatus('Camera access denied or not available. Please check permissions.', 'error');
                }
            });
        }

        // Close scanner events
        [closeScanner, cancelScanner].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', stopQRScanner);
            }
        });

        // Close on modal background click
        if (qrModal) {
            qrModal.addEventListener('click', function(e) {
                if (e.target === qrModal) {
                    stopQRScanner();
                }
            });
        }

        async function startQRScanner() {
            if (isScanning) return;

            // Check if modal element exists
            if (!qrModal) {
                console.error('QR Modal element not found');
                alert('QR Scanner not available');
                return;
            }

            try {
                // Check for camera support
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    throw new Error('Camera API not supported in this browser');
                }

                // Show modal
                qrModal.style.display = 'flex';
                qrModal.style.zIndex = '9999';

                if (statusDiv) {
                    showScannerStatus('Loading QR scanner library...', '');
                }

                // Small delay to ensure modal is visible
                await new Promise(resolve => setTimeout(resolve, 100));

                // Import QR Scanner library
                const QrScanner = (await import('https://cdn.jsdelivr.net/npm/qr-scanner@1.4.2/qr-scanner.min.js')).default;

                showScannerStatus('Starting camera...', '');

                // Check video element
                if (!qrVideo) {
                    throw new Error('Video element not found');
                }

                // Initialize scanner
                qrScanner = new QrScanner(
                    qrVideo,
                    result => handleQRResult(result.data),
                    {
                        returnDetailedScanResult: true,
                        preferredCamera: 'environment',
                        highlightScanRegion: true,
                        highlightCodeOutline: true,
                    }
                );

                await qrScanner.start();
                isScanning = true;
                showScannerStatus('Position the QR code within the camera view', '');

            } catch (error) {
                console.error('QR Scanner error:', error);

                let errorMessage = 'Failed to access camera. ';

                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Camera permission denied. Please allow camera access and try again.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'No camera found on this device.';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += 'Camera not supported in this browser.';
                } else if (error.message.includes('Camera API not supported')) {
                    errorMessage += 'Camera API not supported in this browser.';
                } else if (error.message.includes('Video element not found')) {
                    errorMessage += 'Video element not found in DOM.';
                } else {
                    errorMessage += error.message || 'Unknown error occurred.';
                }

                // Show error in modal if visible, otherwise alert
                if (qrModal && qrModal.style.display === 'flex') {
                    showScannerStatus(errorMessage, 'error');
                    setTimeout(() => {
                        stopQRScanner();
                    }, 5000);
                } else {
                    alert('QR Scanner Error: ' + errorMessage);
                }
            }
        }

        function stopQRScanner() {
            if (qrScanner) {
                qrScanner.stop();
                qrScanner.destroy();
                qrScanner = null;
            }

            isScanning = false;
            qrModal.style.display = 'none';
            showScannerStatus('Position the QR code within the camera view', '');
        }

        function handleQRResult(data) {
            try {
                // Extract item code from QR code URL
                // Expected format: http://<base_url>/{item_code}
                const url = new URL(data);
                const pathParts = url.pathname.split('/').filter(part => part.length > 0);
                const itemCode = pathParts[pathParts.length - 1];

                if (itemCode && itemCode.length >= 4 && itemCode.length <= 8) {
                    showScannerStatus('QR code found! Looking up location...', 'success');
                    lookupItemByCode(itemCode);

                    // Close scanner after successful scan
                    setTimeout(() => {
                        stopQRScanner();
                    }, 1000);
                } else {
                    showScannerStatus('Invalid QR code format. Please scan a valid location QR code.', 'error');
                }

            } catch (error) {
                console.error('Error parsing QR code:', error);
                showScannerStatus('Invalid QR code format. Please scan a valid location QR code.', 'error');
            }
        }

        function lookupItemByCode(itemCode) {
            // Get the location field elements
            const locationInput = document.querySelector('input[name="located_in_search"]');
            const locationHidden = document.querySelector('input[name="located_in"]');
            const locationDisplay = document.getElementById('id_located_in_display');
            const locationClear = document.getElementById('id_located_in_clear');

            if (!locationInput || !locationHidden) {
                console.error('Location field elements not found');
                return;
            }

            // Make AJAX request to find the item by code
            $.ajax({
                url: '/inventory/api/items/autocomplete/',
                dataType: 'json',
                data: {
                    term: itemCode,
                    exact_match: true
                },
                success: function(data) {
                    if (data.results && data.results.length > 0) {
                        const item = data.results[0];

                        // Set the hidden input value to the selected item ID
                        locationHidden.value = item.id;

                        // Show the display field with the selected item
                        if (item.code) {
                            locationDisplay.value = `${item.code} - ${item.text}`;
                        } else {
                            locationDisplay.value = item.text;
                        }

                        // Show the display field and clear button
                        locationDisplay.style.display = 'block';
                        locationClear.style.display = 'block';

                        // Hide the search input
                        locationInput.style.display = 'none';
                    } else {
                        showScannerStatus('Location not found. Please check the QR code or enter manually.', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error looking up item:', error);
                    showScannerStatus('Error looking up location. Please try again.', 'error');
                }
            });
        }

        function showScannerStatus(message, type) {
            if (statusDiv) {
                statusDiv.innerHTML = `<p>${message}</p>`;
                statusDiv.className = 'qr-scanner-status';
                if (type) {
                    statusDiv.classList.add(type);
                }
            }
        }
    }
</script>
{% endblock %}


{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{% if is_add %}Add New Item{% else %}Edit {{ item.item_name }}{% endif %}</h1>
    {% if not is_add %}
    <a href="{% url 'inventory:item_detail' pk=item.pk %}" class="btn btn-outline-secondary">
        Back to Item
    </a>
    {% endif %}
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title">{% if is_add %}New Item Details{% else %}Edit Item Details{% endif %}</h5>
    </div>
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            {% if item_code %}
            <div class="mb-3 row">
                <label class="col-sm-3 col-form-label">Item Code</label>
                <div class="col-sm-9">
                    <div class="form-control-plaintext code-box">{{ item_code }}</div>
                    <div class="form-text">This code will be assigned to the new item.</div>
                </div>
            </div>
            {% endif %}

            <div class="mb-3 row">
                <label for="{{ form.item_name.id_for_label }}" class="col-sm-3 col-form-label">
                    Item Name *
                </label>
                <div class="col-sm-9">
                    {{ form.item_name }}
                    {% if form.item_name.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.item_name.errors }}
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="mb-3 row">
                <label for="{{ form.item_description.id_for_label }}" class="col-sm-3 col-form-label">
                    Description
                </label>
                <div class="col-sm-9">
                    {{ form.item_description }}
                    {% if form.item_description.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.item_description.errors }}
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="mb-3 row">
                <label for="{{ form.item_type.id_for_label }}" class="col-sm-3 col-form-label">
                    Item Type
                </label>
                <div class="col-sm-9">
                    {{ form.item_type }}
                    {% if form.item_type.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.item_type.errors }}
                        </div>
                    {% endif %}
                    <div class="form-text">Select whether this is a regular item or a container/location.</div>
                </div>
            </div>

            <div class="mb-3 row">
                <label for="{{ form.status.id_for_label }}" class="col-sm-3 col-form-label">
                    Status
                </label>
                <div class="col-sm-9">
                    {{ form.status }}
                    {% if form.status.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.status.errors }}
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="mb-3 row">
                <label for="{{ form.located_in.id_for_label }}" class="col-sm-3 col-form-label">
                    Location
                </label>
                <div class="col-sm-9">
                    <div class="location-field-container">
                        {{ form.located_in }}
                        <!-- QR Scanner Button -->
                        <button type="button" id="qr-scanner-btn" class="btn btn-outline-secondary mt-2" style="display: none;">
                            <i class="fas fa-qrcode"></i> Scan QR Code
                        </button>
                        <!-- QR Scanner Modal -->
                        <div id="qr-scanner-modal" class="qr-scanner-modal" style="display: none;">
                            <div class="qr-scanner-content">
                                <div class="qr-scanner-header">
                                    <h5>Scan Location QR Code</h5>
                                    <button type="button" id="close-scanner" class="btn-close">&times;</button>
                                </div>
                                <div class="qr-scanner-body">
                                    <video id="qr-video" class="qr-video"></video>
                                    <div id="qr-scanner-status" class="qr-scanner-status">
                                        <p>Position the QR code within the camera view</p>
                                    </div>
                                </div>
                                <div class="qr-scanner-footer">
                                    <button type="button" id="cancel-scanner" class="btn btn-secondary">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if form.located_in.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.located_in.errors }}
                        </div>
                    {% endif %}
                    <div class="form-text">Select a container or location for this item. On iOS devices, you can also scan a location QR code. Select "Top Level" if this item is not inside any container.</div>
                </div>
            </div>

            <div class="mb-3 row">
                <label for="{{ form.rfid_epc.id_for_label }}" class="col-sm-3 col-form-label">
                    RFID EPC
                </label>
                <div class="col-sm-9">
                    {{ form.rfid_epc }}
                    {% if form.rfid_epc.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.rfid_epc.errors }}
                        </div>
                    {% endif %}
                    <div class="form-text">Optional. Enter the 24-32 character hexadecimal EPC from the RFID tag.</div>
                </div>
            </div>

            <div class="mb-3 row">
                <div class="col-sm-9 offset-sm-3">
                    <button type="submit" class="btn btn-primary">
                        {% if is_add %}Create Item{% else %}Save Changes{% endif %}
                    </button>
                    <a href="{% if is_add %}{% url 'inventory:item_list' %}{% else %}{% url 'inventory:item_by_code' item_code=item.item_code %}{% endif %}" class="btn btn-outline-secondary">
                        Cancel
                    </a>
                </div>
            </div>

            <div class="mb-3 row">
                <label class="col-sm-3 col-form-label">Custom Fields</label>
                <div class="col-sm-9">
                    <div class="card">
                        <div class="card-body">
                            {% if custom_fields %}
                                {% for key, value in custom_fields.items %}
                                <div class="row mb-2 custom-field-row">
                                    <div class="col-5">
                                        <input type="text" class="form-control form-control-sm"
                                              value="{{ key }}" readonly>
                                    </div>
                                    <div class="col-6">
                                        <input type="text" class="form-control form-control-sm"
                                              name="custom_field_{{ key }}" value="{{ value }}">
                                    </div>
                                    <div class="col-1">
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-field">&times;</button>
                                    </div>
                                </div>
                                {% endfor %}
                            {% endif %}

                            <div id="custom-fields-container">
                                <!-- New custom fields will be added here -->
                            </div>

                            <div class="mt-2">
                                <button type="button" id="add-custom-field" class="btn btn-sm btn-outline-secondary">
                                    Add Custom Field
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image upload section with improved UI -->
            <div class="mb-3 row">
                <label class="col-sm-3 col-form-label">Item Image</label>
                <div class="col-sm-9">
                    <div class="card">
                        <div class="card-body">
                            {% if item.image %}
                            <div class="mb-3">
                                <img src="{{ item.image.url }}" class="img-thumbnail" style="max-height: 200px;">
                            </div>
                            {% endif %}

                            <div class="mb-3">
                                <label for="{{ form.image.id_for_label }}" class="form-label">Image File</label>
                                {{ form.image }}
                                {% if form.image.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.image.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text">Upload a clear image of the item.</div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.image_caption.id_for_label }}" class="form-label">Caption</label>
                                {{ form.image_caption }}
                                {% if form.image_caption.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.image_caption.errors }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}
