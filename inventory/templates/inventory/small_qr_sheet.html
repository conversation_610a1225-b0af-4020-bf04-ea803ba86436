{% extends 'base.html' %}

{% block title %}Small QR Labels - Inventory Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Small QR Labels</h1>
    <div>
        <button onclick="window.print()" class="btn btn-primary">
            <i class="bi bi-printer"></i> Print Labels
        </button>
        <form method="post" class="d-inline">
            {% csrf_token %}
            <input type="hidden" name="download_pdf" value="1">
            {% for label in labels %}
            {% if not label.is_blank %}
            <input type="hidden" name="generated_codes" value="{{ label.code }}">
            {% endif %}
            {% endfor %}
            <button type="submit" class="btn btn-outline-primary">
                <i class="bi bi-file-earmark-pdf"></i> Download PDF
            </button>
        </form>
        <form method="post" class="d-inline">
            {% csrf_token %}
            <input type="hidden" name="download_csv" value="1">
            {% for label in labels %}
            {% if not label.is_blank %}
            <input type="hidden" name="generated_codes" value="{{ label.code }}">
            {% endif %}
            {% endfor %}
            <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-file-earmark-text"></i> Download CSV
            </button>
        </form>
        <a href="{% url 'inventory:generate_labels' %}" class="btn btn-outline-secondary">
            Generate More
        </a>
    </div>
</div>

<div class="alert alert-info mb-4">
    <h5>Ready to Print</h5>
    <p class="mb-0">
        {{ unique_codes }} unique QR code labels have been generated. Click the "Print Labels" button above to print this page,
        or download a PDF or CSV file.
    </p>
</div>

<div class="small-qr-sheet">
    {% for label in labels %}
    <div class="small-qr-label">
        {% if not label.is_blank %}
        <img src="data:image/png;base64,{{ label.qrcode }}" alt="QR Code for {{ label.code }}" class="small-qrcode-image">
        <div class="small-code">{{ label.code }}</div>
        {% endif %}
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block extra_js %}
<style>
    @media print {
        body {
            margin: 0;
            padding: 0;
            background: white;
        }
        
        .navbar, 
        .alert,
        .btn,
        h1, 
        .d-flex {
            display: none !important;
        }
        
        .container {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
        }
    }
    
    .small-qr-sheet {
        width: 8.5in;
        height: 11in;
        padding-top: 0.375in;
        padding-bottom: 0.375in;
        padding-left: 0.25in;
        padding-right: 0.25in;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
    }
    
    .small-qr-label {
        width: 0.5in;
        height: 0.5in;
        margin-right: 0.25in;
        margin-bottom: 0.25in;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .small-qr-label:nth-child(11n) {
        margin-right: 0;
    }
    
    .small-qrcode-image {
        width: 0.4in;
        height: 0.4in;
    }
    
    .small-code {
        font-size: 4pt;
        line-height: 1;
        font-family: monospace;
    }
</style>
{% endblock %}
